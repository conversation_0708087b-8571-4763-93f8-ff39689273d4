{"builds": [{"archives": [{"base_path": "out/ci/android_profile/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_profile/zip_archives/android-arm-profile/windows-x64.zip"], "name": "ci/android_profile", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile", "--runtime-mode", "profile", "--android", "--no-goma", "--rbe"], "name": "ci\\android_profile", "description": "Produces profile mode artifacts to target 32-bit arm Android from a Windows host.", "ninja": {"config": "ci/android_profile", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}, {"archives": [{"base_path": "out/ci/android_profile_arm64/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_profile_arm64/zip_archives/android-arm64-profile/windows-x64.zip"], "name": "ci/android_profile_arm64", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile_arm64", "--runtime-mode", "profile", "--android", "--android-cpu=arm64", "--no-goma", "--rbe"], "name": "ci\\android_profile_arm64", "description": "Produces profile mode artifacts to target 64-bit arm Android from a Windows host.", "ninja": {"config": "ci/android_profile_arm64", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}, {"archives": [{"base_path": "out/ci/android_profile_x64/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_profile_x64/zip_archives/android-x64-profile/windows-x64.zip"], "name": "ci/android_profile_x64", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_profile_x64", "--runtime-mode", "profile", "--android", "--android-cpu=x64", "--no-goma", "--rbe"], "name": "ci\\android_profile_x64", "description": "Produces profile mode artifacts to target x64 Android from a Windows host.", "ninja": {"config": "ci/android_profile_x64", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}, {"archives": [{"base_path": "out/ci/android_release/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_release/zip_archives/android-arm-release/windows-x64.zip"], "name": "ci/android_release", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release", "--runtime-mode", "release", "--android", "--no-goma", "--rbe"], "name": "ci\\android_release", "description": "Produces release mode artifacts to target 32-bit arm Android from a Windows host.", "ninja": {"config": "ci/android_release", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}, {"archives": [{"base_path": "out/ci/android_release_arm64/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_release_arm64/zip_archives/android-arm64-release/windows-x64.zip"], "name": "ci/android_release_arm64", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release_arm64", "--runtime-mode", "release", "--android", "--android-cpu=arm64", "--no-goma", "--rbe"], "name": "ci\\android_release_arm64", "description": "Produces release mode artifacts to target 64-bit arm Android from a Windows host.", "ninja": {"config": "ci/android_release_arm64", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}, {"archives": [{"base_path": "out/ci/android_release_x64/zip_archives/", "type": "gcs", "include_paths": ["out/ci/android_release_x64/zip_archives/android-x64-release/windows-x64.zip"], "name": "ci/android_release_x64", "realm": "production"}], "drone_dimensions": ["device_type=none", "os=Windows-10"], "gclient_variables": {"use_rbe": true}, "gn": ["--target-dir", "ci/android_release_x64", "--runtime-mode", "release", "--android", "--android-cpu=x64", "--no-goma", "--rbe"], "name": "ci\\android_release_x64", "description": "Produces release mode artifacts to target x64 Android from a Windows host.", "ninja": {"config": "ci/android_release_x64", "targets": ["flutter/build/archives:archive_win_gen_snapshot"]}}]}