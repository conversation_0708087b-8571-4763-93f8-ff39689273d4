import 'package:drift/drift.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/database_provider.dart';
import '../types/health_types.dart';
import 'database.dart';

part 'health_record_repository.g.dart';

/// 统一健康记录仓库
class HealthRecordRepository {
  final HealthDiaryDatabase _database;

  HealthRecordRepository(this._database);

  Future<int> addHealthRecord({
    required HealthRecordTypeEnum type,
    required HealthRecordData data,
    required DateTime createdAt,
  }) {
    final companion = HealthRecordsCompanion(
      type: Value(type),
      data: Value(data),
      createdAt: Value(createdAt),
    );

    return _database.into(_database.healthRecords).insert(companion);
  }

  /// 更新健康记录
  Future<bool> updateHealthRecord(HealthRecord record) {
    final companion = HealthRecordsCompanion(
      id: Value(record.id),
      type: Value(record.type),
      data: Value(record.data),
      createdAt: Value(record.createdAt),
    );
    return _database.update(_database.healthRecords).replace(companion);
  }

  /// 删除健康记录
  Future<int> deleteHealthRecord(int id) {
    return (_database.delete(
      _database.healthRecords,
    )..where((t) => t.id.equals(id))).go();
  }

  /// 分页获取健康记录（监听）
  Stream<List<HealthRecord>> watchHealthRecordsPaginated({
    required int page,
    required int pageSize,
  }) {
    final offset = (page - 1) * pageSize;
    return (_database.select(_database.healthRecords)
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
          ..limit(pageSize, offset: offset))
        .watch();
  }

  /// 分页获取健康记录
  Future<List<HealthRecord>> getHealthRecordsPaginated({
    required int page,
    required int pageSize,
  }) {
    final offset = (page - 1) * pageSize;
    return (_database.select(_database.healthRecords)
      ..orderBy([(t) => OrderingTerm.desc(t.createdAt)])
      ..limit(pageSize, offset: offset))
        .get();
  }

  /// 获取今天的血压记录
  Future<List<HealthRecord>> getTodayBloodPressureRecords() async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodPressure.name))
          ..where((t) => t.createdAt.isBetweenValues(todayStart, todayEnd))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .get();
  }

  /// 获取今天的血糖记录
  Future<List<HealthRecord>> getTodayBloodSugarRecords() async {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return (_database.select(_database.healthRecords)
          ..where((t) => t.type.equals(HealthRecordTypeEnum.bloodSugar.name))
          ..where((t) => t.createdAt.isBetweenValues(todayStart, todayEnd))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .get();
  }

  /// 获取今天的所有健康记录
  Stream<List<HealthRecord>> watchTodayHealthRecords() {
    final now = DateTime.now();
    final todayStart = DateTime(now.year, now.month, now.day);
    final todayEnd = todayStart.add(const Duration(days: 1));

    return (_database.select(_database.healthRecords)
          ..where((t) => t.createdAt.isBetweenValues(todayStart, todayEnd))
          ..orderBy([(t) => OrderingTerm.desc(t.createdAt)]))
        .watch();
  }
}

/// 统一健康记录仓库提供者
@riverpod
HealthRecordRepository healthRecordRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return HealthRecordRepository(database);
}
