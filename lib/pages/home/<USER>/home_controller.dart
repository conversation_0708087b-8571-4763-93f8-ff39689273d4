import 'package:health_diary/types/health_types.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../types/health_record.dart';
import '../../../repository/health_record_repository.dart';
import '../../../health_service/service_manager.dart';

part 'home_controller.g.dart';

/// 首页控制器
@riverpod
class HomeController extends _$HomeController {
  @override
  Stream<Map<HealthRecordTypeEnum, TodayHealthOverview>> build() {
    final healthRepo = ref.watch(healthRecordRepositoryProvider);

    // 监听今天的所有健康记录变化
    return healthRepo.watchTodayHealthRecords().map((todayRecords) {
      // 使用 HealthServiceManager 转换为 TodayHealthOverview
      return HealthServiceManager.convertToTodayOverview(todayRecords);
    });
  }
}
